package ai.friday.dda.adapters.jobs

import ai.friday.dda.adapters.aws.LambdaInvoker
import ai.friday.dda.adapters.configuration.EachTenant
import ai.friday.dda.app.job.AbstractJob
import ai.friday.dda.app.tenant.TenantConfiguration
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@EachTenant
open class DDATriggerLambdaJob(
    private val configuration: TenantConfiguration,
    private val lambdaInvoker: LambdaInvoker,
    @Property(name = "aws.lambda.dda-files.functionName") private val lambdaFunctionName: String
) : AbstractJob(cron = "*/10 * * * *") {

    private val logger = LoggerFactory.getLogger(DDATriggerLambdaJob::class.java)

    @NewSpan
    override fun execute() {
        val tenantName = configuration.tenantName.value
        val markers = Markers.append("tenant", tenantName)

        logger.info(markers, "Triggering lambda for tenant: $tenantName")

        val payload = mapOf("environment" to tenantName)

        val result = lambdaInvoker.invoke(lambdaFunctionName, payload)

        result.fold(
            { error ->
                logger.error(markers.and(Markers.append("error", error.toString())),
                    "Failed to trigger lambda for tenant: $tenantName")
            },
            { response ->
                logger.info(markers.and(Markers.append("response", response)),
                    "Successfully triggered lambda for tenant: $tenantName")
            }
        )
    }
}