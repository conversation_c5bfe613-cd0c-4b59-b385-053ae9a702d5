package ai.friday.dda.adapters.aws

import ai.friday.dda.Err
import ai.friday.dda.ServerError
import ai.friday.dda.write
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.lambda.LambdaClient
import software.amazon.awssdk.services.lambda.model.InvokeRequest
import software.amazon.awssdk.services.lambda.model.InvokeResponse
import software.amazon.awssdk.services.lambda.model.LambdaException

@Singleton
class LambdaInvoker(private val lambdaClient: LambdaClient) {

    private val logger = LoggerFactory.getLogger(LambdaInvoker::class.java)

    fun invoke(functionName: String, payload: Any): Either<Err, String> {
        val markers = Markers.append("function_name", functionName)
        
        return try {
            val payloadJson = write(payload)
            
            logger.debug(markers.and(Markers.append("payload", payloadJson)), "Invoking lambda function")
            
            val invokeRequest = InvokeRequest.builder()
                .functionName(functionName)
                .payload(SdkBytes.fromUtf8String(payloadJson))
                .build()

            val response: InvokeResponse = lambdaClient.invoke(invokeRequest)
            
            if (response.statusCode() in 200..299) {
                val responsePayload = response.payload().asUtf8String()
                logger.debug(markers.and(Markers.append("status_code", response.statusCode())), 
                    "Lambda invocation successful")
                responsePayload.right()
            } else {
                val error = "Lambda invocation failed with status code: ${response.statusCode()}"
                logger.error(markers.and(Markers.append("status_code", response.statusCode())), error)
                ServerError(error).left()
            }
            
        } catch (e: LambdaException) {
            val error = "Lambda service error: ${e.message}"
            logger.error(markers, error, e)
            ServerError(error, e).left()
        } catch (e: Exception) {
            val error = "Unexpected error invoking lambda: ${e.message}"
            logger.error(markers, error, e)
            ServerError(error, e).left()
        }
    }
}
