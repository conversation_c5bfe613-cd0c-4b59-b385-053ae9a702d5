package ai.friday.dda.adapters.jobs

import ai.friday.dda.Err
import ai.friday.dda.ServerError
import ai.friday.dda.adapters.aws.LambdaInvoker
import ai.friday.dda.app.tenant.TenantConfiguration
import ai.friday.dda.app.tenant.TenantName
import arrow.core.left
import arrow.core.right
import io.kotest.core.spec.style.FunSpec
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify

class DDATriggerLambdaJobTest : FunSpec({

    val lambdaInvoker: LambdaInvoker = mockk(relaxed = true)
    val tenantConfiguration: TenantConfiguration = mockk(relaxed = true) {
        every { tenantName } returns TenantName("FRIDAY")
    }
    val lambdaFunctionName = "dda-files"

    val job = DDATriggerLambdaJob(
        tenantConfiguration,
        lambdaInvoker,
        lambdaFunctionName
    )

    test("should invoke lambda with correct tenant environment") {
        // Given
        every { lambdaInvoker.invoke(any(), any()) } returns "success".right()

        // When
        job.execute()

        // Then
        verify {
            lambdaInvoker.invoke(
                lambdaFunctionName,
                mapOf("environment" to "FRIDAY")
            )
        }
    }

    test("should handle lambda invocation failure gracefully") {
        // Given
        val error = ServerError("Lambda invocation failed")
        every { lambdaInvoker.invoke(any(), any()) } returns error.left()

        // When
        job.execute()

        // Then
        verify {
            lambdaInvoker.invoke(
                lambdaFunctionName,
                mapOf("environment" to "FRIDAY")
            )
        }
    }
})
